<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>top-level-items</key>
	<array>
		<dict>
			<key>destination</key>
			<dict>
				<key>location-parameters</key>
				<dict>
					<key>EndingColumnNumber</key>
					<string>4</string>
					<key>EndingLineNumber</key>
					<string>106</string>
					<key>StartingColumnNumber</key>
					<string>4</string>
					<key>StartingLineNumber</key>
					<string>106</string>
					<key>Timestamp</key>
					<string>777717738.966309</string>
				</dict>
				<key>rebasable-url</key>
				<dict>
					<key>base</key>
					<string>workspace</string>
					<key>payload</key>
					<dict>
						<key>relative-path</key>
						<string>CarbonCoin/Utilities/AppSettings.swift</string>
					</dict>
				</dict>
			</dict>
			<key>text-context</key>
			<dict>
				<key>focused</key>
				<string></string>
				<key>leading</key>
				<string>        loadSettings()
        updateUsageDays()
    </string>
				<key>trailing</key>
				<string>    setupNotificationObservers()

        // 如果用户已经登录，从云端加载用户信息（包括头像）
</string>
			</dict>
			<key>type</key>
			<string>bookmark</string>
		</dict>
		<dict>
			<key>destination</key>
			<dict>
				<key>location-parameters</key>
				<dict>
					<key>EndingColumnNumber</key>
					<string>0</string>
					<key>EndingLineNumber</key>
					<string>113</string>
					<key>StartingColumnNumber</key>
					<string>0</string>
					<key>StartingLineNumber</key>
					<string>112</string>
					<key>Timestamp</key>
					<string>777538313.857661</string>
				</dict>
				<key>rebasable-url</key>
				<dict>
					<key>base</key>
					<string>workspace</string>
					<key>payload</key>
					<dict>
						<key>relative-path</key>
						<string>CarbonCoin/Utilities/AppSettings.swift</string>
					</dict>
				</dict>
			</dict>
			<key>text-context</key>
			<dict>
				<key>focused</key>
				<string>    }
</string>
				<key>leading</key>
				<string>            await checkAndLoadUserInfoOnStartup()
        }
</string>
				<key>trailing</key>
				<string>
    deinit {
        NotificationCenter.default.removeObserver(self)
</string>
			</dict>
			<key>type</key>
			<string>bookmark</string>
		</dict>
	</array>
</dict>
</plist>
