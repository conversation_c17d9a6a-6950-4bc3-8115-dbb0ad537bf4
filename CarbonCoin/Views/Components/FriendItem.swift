//
//  FriendItem.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/28.
//

import SwiftUI

// MARK: - 好友信息组件

/// 显示单个用户信息的卡片组件
struct FriendItem: View {

    // MARK: - Properties

    let userInfo: UserInfo
    let relationshipState: FriendRelationshipState
    let onButtonTap: () -> Void
    let onAcceptTap: (() -> Void)?
    let onRejectTap: (() -> Void)?

    // MARK: - Initialization

    init(
        userInfo: UserInfo,
        relationshipState: FriendRelationshipState,
        onButtonTap: @escaping () -> Void,
        onAcceptTap: (() -> Void)? = nil,
        onRejectTap: (() -> Void)? = nil
    ) {
        self.userInfo = userInfo
        self.relationshipState = relationshipState
        self.onButtonTap = onButtonTap
        self.onAcceptTap = onAcceptTap
        self.onRejectTap = onRejectTap
    }

    // MARK: - Body

    var body: some View {
        HStack(spacing: Theme.Spacing.md) {
            // 用户头像
            userAvatarView

            // 用户信息
            userInfoView

            Spacer()

            // 操作按钮区域
            actionButtonsView
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
    }

    // MARK: - Private Views

    /// 用户头像视图
    private var userAvatarView: some View {
        Circle()
            .fill(Color.primaryGradient.opacity(0.7))
            .frame(width: 50, height: 50)
            .overlay(
                Group {
                    if let avatar = userInfo.avatar, !avatar.isEmpty {
                        // 如果有头像URL，这里可以加载网络图片
                        // AsyncImage(url: URL(string: avatar)) { image in
                        //     image.resizable()
                        // } placeholder: {
                        //     defaultAvatarView
                        // }
                        defaultAvatarView // 暂时使用默认头像
                    } else {
                        defaultAvatarView
                    }
                }
            )
            .overlay(
                Circle()
                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
            )
    }

    /// 默认头像视图
    private var defaultAvatarView: some View {
        Text(String(userInfo.nickname.prefix(1)))
            .font(.title2Brand)
            .foregroundColor(.white)
            .fontWeight(.medium)
    }

    /// 用户信息视图
    private var userInfoView: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
            // 用户昵称
            Text(userInfo.nickname)
                .font(.bodyBrand)
                .foregroundColor(.textPrimary)
                .fontWeight(.medium)

            // 用户ID
            Text("@\(userInfo.userId)")
                .font(.captionBrand)
                .foregroundColor(.textSecondary)

            // 关系状态描述
            if relationshipState != .notFriend {
                Text(relationshipState.statusDescription)
                    .font(.caption2)
                    .foregroundColor(statusColor)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(statusColor.opacity(0.2))
                    )
            }
        }
    }

    /// 操作按钮视图
    private var actionButtonsView: some View {
        Group {
            if relationshipState == .pendingReceived {
                // 待确认状态显示接受和拒绝按钮
                HStack(spacing: Theme.Spacing.sm) {
                    // 拒绝按钮
                    Button(action: {
                        onRejectTap?()
                    }) {
                        Image(systemName: "xmark")
                            .font(.caption)
                            .foregroundColor(.red)
                            .frame(width: 32, height: 32)
                            .background(
                                Circle()
                                    .fill(Color.red.opacity(0.1))
                            )
                    }

                    // 接受按钮
                    Button(action: {
                        onAcceptTap?()
                    }) {
                        Image(systemName: "checkmark")
                            .font(.caption)
                            .foregroundColor(.success)
                            .frame(width: 32, height: 32)
                            .background(
                                Circle()
                                    .fill(Color.success.opacity(0.1))
                            )
                    }
                }
            } else {
                // 其他状态显示单个按钮
                Button(action: onButtonTap) {
                    Text(relationshipState.buttonTitle)
                        .font(.captionBrand)
                        .foregroundColor(buttonTextColor)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            Capsule()
                                .fill(buttonBackgroundColor)
                        )
                }
                .disabled(!relationshipState.isButtonEnabled)
            }
        }
    }

    // MARK: - Computed Properties

    /// 状态颜色
    private var statusColor: Color {
        switch relationshipState {
        case .notFriend:
            return .textSecondary
        case .pendingSent:
            return .auxiliaryYellow
        case .pendingReceived:
            return .success
        case .friend:
            return .success
        case .rejected:
            return .red
        }
    }

    /// 按钮文本颜色
    private var buttonTextColor: Color {
        switch relationshipState {
        case .notFriend, .rejected:
            return .white
        case .pendingSent:
            return .textSecondary
        case .pendingReceived:
            return .success
        case .friend:
            return Color(hex: "4B7905")
        }
    }

    /// 按钮背景颜色
    private var buttonBackgroundColor: Color {
        switch relationshipState {
        case .notFriend, .rejected:
            return Color(hex: "4B7905")
        case .pendingSent:
            return Color.gray.opacity(0.3)
        case .pendingReceived:
            return Color.success.opacity(0.2)
        case .friend:
            return Color.clear
        }
    }
}

// MARK: - 预览

#Preview {
    VStack(spacing: 16) {
        // 不是好友状态
        FriendItem(
            userInfo: UserInfo(userId: "alice", nickname: "Alice", avatar: nil),
            relationshipState: .notFriend,
            onButtonTap: { print("Add friend tapped") }
        )

        // 已发送请求状态
        FriendItem(
            userInfo: UserInfo(userId: "bob", nickname: "Bob", avatar: nil),
            relationshipState: .pendingSent,
            onButtonTap: { print("Pending sent tapped") }
        )

        // 待确认状态
        FriendItem(
            userInfo: UserInfo(userId: "charlie", nickname: "Charlie", avatar: nil),
            relationshipState: .pendingReceived,
            onButtonTap: { print("Pending received tapped") },
            onAcceptTap: { print("Accept tapped") },
            onRejectTap: { print("Reject tapped") }
        )

        // 已是好友状态
        FriendItem(
            userInfo: UserInfo(userId: "david", nickname: "David", avatar: nil),
            relationshipState: .friend,
            onButtonTap: { print("View profile tapped") }
        )
    }
    .padding()
    .stableBackground()
}
